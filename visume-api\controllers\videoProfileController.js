const {
  getUploadUrlFromCacheOrNew,
  generateScores,
  generateVideoRandomId,
  generateSingleQuestion,
} = require("../utils/helpers");
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// 🎯 MEMBERSHIP HELPER: Check candidate's membership status and Visume limits
async function checkCandidateMembershipStatus(candId) {
  try {
    // Get candidate's database ID from cand_id
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id: candId },
      select: { id: true }
    });

    if (!candidate) {
      throw new Error('Candidate not found');
    }

    // Get candidate's current plan
    const candidatePlan = await prisma.jobseekerplans.findFirst({
      where: { cand_id: candidate.id },
      include: { plans: true },
      orderBy: { start_date: 'desc' }
    });

    // Count current Visumes
    const currentVisumeCount = await prisma.videoprofile.count({
      where: { cand_id: candId }
    });

    // If no plan exists, they get 1 free Visume (default behavior)
    if (!candidatePlan) {
      return {
        canCreateVisume: currentVisumeCount < 1,
        currentVisumeCount,
        allowedVisumes: 1,
        planName: 'Free Plan',
        hasActivePlan: false
      };
    }

    // Check plan-based limits using new visume_limit field
    const allowedVisumes = candidatePlan.visume_limit || candidatePlan.credits || 1; // Use visume_limit, fallback to credits, then default to 1
    const visumesUsed = candidatePlan.visumes_used || currentVisumeCount;
    const canCreateVisume = visumesUsed < allowedVisumes;

    return {
      canCreateVisume,
      currentVisumeCount,
      allowedVisumes,
      visumesUsed,
      planName: candidatePlan.plans.plan_name,
      hasActivePlan: true,
      planEndDate: candidatePlan.end_date,
      planId: candidatePlan.id
    };
  } catch (error) {
    console.error('Error checking candidate membership status:', error);
    // Default to allowing 1 free Visume on error
    const currentVisumeCount = await prisma.videoprofile.count({
      where: { cand_id: candId }
    });

    return {
      canCreateVisume: currentVisumeCount < 1,
      currentVisumeCount,
      allowedVisumes: 1,
      planName: 'Free Plan',
      hasActivePlan: false,
      error: true
    };
  }
}

// Video Profile Creation API
exports.createVideoProfile = async (req, res) => {
  const { candId, jobRole, skills, companyType, experience, salary, resumeData } = req.body;

  // Validate the required fields
  if (
    !candId ||
    !jobRole ||
    !skills ||
    !companyType ||
    !experience ||
    !salary
  ) {
    return res.status(400).json({
      message: "All fields are required",
      details: "Please provide all required fields: candId, jobRole, skills, companyType, experience, and salary"
    });
  }

  // Validate company type
  if (!['startup', 'mid_range', 'mnc'].includes(companyType.toLowerCase())) {
    return res.status(400).json({
      message: "Invalid company type",
      details: "Company type must be 'startup', 'mid_range', or 'mnc'"
    });
  }

  // Normalize company type to lowercase
  const normalizedCompanyType = companyType.toLowerCase();
  
  // Map experience string to enum value
  const experienceEnumMap = {
    "0-1": "ZeroToOne",
    "2-3": "TwoToThree",
    "3-5": "ThreeToFive"
  };
  const normalizedExperience = experienceEnumMap[experience] || experience;

  try {
    // Process the skills field
    const skillsString = skills.join(", ");

    // 🎯 MEMBERSHIP VALIDATION: Check candidate's Visume limit
    const membershipStatus = await checkCandidateMembershipStatus(candId);

    if (!membershipStatus.canCreateVisume) {
      return res.status(403).json({
        message: "You have used your 1 free Visume. To create more Visumes, please contact us.",
        membershipStatus: {
          currentVisumeCount: membershipStatus.currentVisumeCount,
          allowedVisumes: membershipStatus.allowedVisumes,
          planName: membershipStatus.planName,
          needsUpgrade: true
        },
        whatsappContact: "[WHATSAPP_NUMBER_PLACEHOLDER]"
      });
    }

    // Check if the same role and skills already exist for the same candidate
    const existingProfile = await prisma.videoprofile.findFirst({
      where: {
        cand_id: candId,
        role: jobRole,
        skills: skillsString,
      },
    });

    if (existingProfile) {
      return res.status(400).json({
        message: "A video profile with the same role and skills already exists.",
      });
    }

    // Generate a random ID for the video profile
    const videoProfileId = generateVideoRandomId();

    // Generate just the first question with proper formatting
    let questions;
    try {
      // Extract projects from completeResumeData if available
      const projects =
        req.body.completeResumeData && Array.isArray(req.body.completeResumeData.projects)
          ? req.body.completeResumeData.projects
          : [];
      const generatedQuestion = await generateSingleQuestion(
        jobRole,
        [], // No previous Q&A for first question
        skills,
        true, // isFirstQuestion
        normalizedCompanyType,
        experience,
        null, // Let AI decide type
        1,    // currentQuestionNumber
        projects // Pass projects for first question
      );

      const firstQuestion = {
        questionId: 1, // Assign a unique ID, starting with 1 for the first question
        ...generatedQuestion,
        startTimestamp: null,
        endTimestamp: null,
        answer: null,
        scores: {
          communication: 0,
          technical: 0,
          overall: 0
        },
        follow_up: generatedQuestion.follow_up || "Please provide specific examples from your experience"
      };

      questions = [firstQuestion]; // Array with single question
    } catch (error) {
      console.error("Error generating first question:", error);
      throw error;
    }

    // Requirements generation removed - no longer using AI-driven requirement assessment

    // Prepare the video profile data for insertion into the database
    const videoProfileData = {
      videoProfileId,
      candId,
      jobRole,
      skills: skills,
      companyType,
      experience,
      salary,
      questions: questions, // Use the generated first question
      videoUrl: null,
      score: null,
      status: "started",
    };

    // Insert the new video profile using Prisma
    await prisma.videoprofile.create({
      data: {
        video_profile_id: videoProfileId,
        cand_id: candId,
        role: jobRole,
        skills: skillsString,
        job_type: normalizedCompanyType,
        experience_range: normalizedExperience,
        salary: JSON.stringify(salary),
        questions: JSON.stringify(questions),
        video_url: null,
        score: null,
        status: "started",
      },
    });

    // 🎯 VISUME USAGE TRACKING: Increment visumes_used counter after successful creation
    if (membershipStatus.hasActivePlan && membershipStatus.planId) {
      try {
        await prisma.jobseekerplans.update({
          where: { id: membershipStatus.planId },
          data: {
            visumes_used: { increment: 1 }
          }
        });
      } catch (error) {
        console.error('Error updating visume usage counter:', error);
        // Don't fail the entire request if usage tracking fails
      }
    }

    res.json({
      videoProfileId,
      candId,
      jobRole,
      skills: skills,
      companyType,
      experience,
      salary,
      questions,
    });
  } catch (error) {
    console.error("Error creating video profile:", error);
    res.status(500).send({
      message: "Failed to create video profile.",
    });
  }
};


// Generate Score Creation API
exports.generateScore = async (req, res) => {
  console.log("=== generateScore API called ===");
  console.log("Request body:", JSON.stringify(req.body, null, 2));

  const { questions } = req.body;

  // Validate the required fields
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    console.log("Validation failed: questions must be a non-empty array");
    return res.status(400).json({
      message: "Questions must be a non-empty array of questions and answers."
    });
  }

  // Validate each question has required structure
  const isValidStructure = questions.every(q => q.question && q.answer);
  if (!isValidStructure) {
    console.log("Validation failed: invalid question structure");
    return res.status(400).json({
      message: "Each question must have 'question' and 'answer' fields."
    });
  }

  // Retry logic for scoring
  const maxRetries = 3;
  let attempt = 0;
  let lastError = null;
  let scores = null;

  while (attempt < maxRetries) {
    try {
      console.log(`Calling generateScores function (attempt ${attempt + 1})...`);
      scores = await generateScores(questions);
      console.log("Score generation successful:", scores);
      res.json(scores);
      return;
    } catch (error) {
      lastError = error;
      console.error(`Error in score generation (attempt ${attempt + 1}):`, error);
      if (attempt < maxRetries - 1) {
        // Wait 500ms before retrying
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    attempt++;
  }

  // If all retries failed
  console.error("Score generation failed after retries:", lastError);
  res.status(500).json({
    message: "Failed to generate report after multiple attempts. Please contact support.",
    error: lastError?.message || "Unknown error"
  });
};

// Finish Video Profile API
exports.addVideoProfileData = async (req, res) => {
  const { videoProfileId, videoUrl, score, questions } = req.body;

  // Validate the required fields
  if (!videoProfileId || !videoUrl || !score || !questions) {
    return res.status(400).send("All fields are required.");
  }

  try {
    // Check if the video profile exists
    const existingProfile = await prisma.videoprofile.findFirst({
      where: { video_profile_id: videoProfileId },
      select: { id: true }
    });

    if (!existingProfile) {
      return res.status(404).send("Video profile not found.");
    }
    const updateId = existingProfile.id;

    let questionsString;
    try {
      questionsString = JSON.stringify(questions);
    } catch (jsonError) {
      console.error("Error stringifying questions:", jsonError);
      return res.status(400).send("Invalid questions format.");
    }

    let scoreString;
    try {
      scoreString = JSON.stringify(score);
    } catch (jsonError) {
      console.error("Error stringifying scores:", jsonError);
      return res.status(400).send("Invalid scores format.");
    }

    await prisma.videoprofile.update({
      where: { id: updateId },
      data: {
        video_url: videoUrl,
        score: scoreString,
        status: "notsubmitted",
        questions: questionsString,
      },
    });

    res.json({
      message: "Video profile updated successfully.",
      videoProfileId,
      videoUrl,
      score,
      questions,
      status: "notSubmitted",
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};

// Complete Video Profile API
exports.finishVideoProfile = async (req, res) => {
  const { score, videoProfileId } = req.body;
  // Validate the required fields
  if (!videoProfileId || !score.toString()) {
    return res.status(400).json({ msg: "All fields are required." });
  }
  if (score < 5) {
    return res
      .status(429)
      .json({ msg: "Score is too low , Video Resume Can't be Submitted." });
  }
  try {
    // Check if the video profile exists
    const existingProfile = await prisma.videoprofile.findFirst({
      where: { video_profile_id: videoProfileId },
      select: { id: true }
    });

    if (!existingProfile) {
      return res.status(404).send("Video profile not found.");
    }
    const updateId = existingProfile.id;

    const newStatus = score > 5 ? "active" : "inactive";

    await prisma.videoprofile.update({
      where: { id: updateId },
      data: { status: newStatus },
    });

    res.json({
      message: "Video profile finished successfully.",
      status: newStatus,
    });
  } catch (error) {
    console.error("Error finishing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to finish the video profile.", error });
  }
};

// Fetch all video profiles
exports.listVideoProfiles = async (req, res) => {
  try {
    const profiles = await prisma.videoprofile.findMany();
    res.json(convertBigInt(profiles));
  } catch (error) {
    console.error("Error listing video profiles:", error);
    res.status(500).send("Failed to list video profiles.");
  }
};

// Fetch video profiles by candidate ID
exports.listVideoProfilesByCandidateId = async (req, res) => {
  const { cand_id } = req.params;

  try {
    const profiles = await prisma.videoprofile.findMany({
      where: { cand_id: cand_id },
    });
    res.json(convertBigInt(profiles));
  } catch (error) {
    console.error("Error listing video profiles by candidate ID:", error);
    res.status(500).send("Failed to list video profiles.");
  }
};

// Update video profile questions
exports.updateVideoProfileQuestions = async (req, res) => {
  const { vpid } = req.params;
  const { questionId, updatedQuestion } = req.body;

  console.log("Received request to updateVideoProfileQuestions:");
  console.log("req.params:", req.params);
  console.log("req.body:", req.body);

  // Validate the required fields
  if (!vpid) {
    return res.status(400).json({
      message: "Video profile ID is required"
    });
  }

  if (!updatedQuestion) {
    return res.status(400).json({
      message: "updatedQuestion is required"
    });
  }

  try {
    const profile = await prisma.videoprofile.findFirst({
      where: { video_profile_id: vpid },
      select: { questions: true }
    });

    if (!profile) {
      return res.status(404).json({
        message: "Video profile not found"
      });
    }

    const existingQuestions = JSON.parse(profile.questions || "[]");
    let questionFound = false;

    let newQuestions;
    if (questionId) {
      // Attempt to update an existing question if questionId is provided
      newQuestions = existingQuestions.map((q) => {
        if (Number(q.questionNumber) === Number(questionId)) {
          questionFound = true;
          return { ...q, ...updatedQuestion };
        }
        return q;
      });
      if (!questionFound) {
        return res.status(404).json({
          message: "Question not found in video profile for update"
        });
      }
    } else {
      // If no questionId is provided, append the new question
      newQuestions = [...existingQuestions, updatedQuestion];
      questionFound = true;
    }

    if (!questionFound) {
      return res.status(404).json({
        message: "Question not found in video profile"
      });
    }

    // Update the questions field using Prisma
    await prisma.videoprofile.updateMany({
      where: { video_profile_id: vpid },
      data: { questions: JSON.stringify(newQuestions) },
    });

    res.json({
      message: "Question updated successfully",
      videoProfileId: vpid,
      questions: newQuestions
    });
  } catch (error) {
    console.error("Error updating video profile questions:", error);
    res.status(500).json({
      message: "Failed to update video profile questions",
      error: error.message
    });
  }
};

// Fetch candidate profile Details and video resume by candidate ID
/**
 * Recursively converts all BigInt values in an object to strings.
 */
function convertBigInt(obj) {
  if (typeof obj === 'bigint') return obj.toString();
  if (obj instanceof Date) return obj.toISOString();
  if (Array.isArray(obj)) return obj.map(convertBigInt);
  if (obj && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj).map(([k, v]) => [k, convertBigInt(v)])
    );
  }
  return obj;
}

exports.listCandidateAndVideoProfilesByCandidateId = async (req, res) => {
  const { cand_id } = req.params;

  try {
    // Fetch candidate profile
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id: cand_id },
    });

    if (!candidate) {
      return res.status(404).json({ message: "Candidate not found" });
    }

    // Parse stripped_resume if present
    let parsedResume = null;
    if (candidate.stripped_resume) {
      try {
        parsedResume = JSON.parse(candidate.stripped_resume);
      } catch (e) {
        console.error("Error parsing stripped_resume:", e);
      }
    }
    candidate.stripped_resume = parsedResume;

    // Fetch all video profiles for this candidate
    const videoProfiles = await prisma.videoprofile.findMany({
      where: { cand_id: cand_id },
    });

    // Initialize counters for interactions and status
    let interactionCount = { click: 0, view: 0 };
    let statusCount = { shortlisted: 0, unlocked: 0 };

    // For each video profile, aggregate analytics and status counts
    await Promise.all(
      videoProfiles.map(async (video) => {
        // Count analytics by interaction_type
        const analytics = await prisma.analytics.groupBy({
          by: ['interaction_type'],
          where: { profile_id: video.id },
          _count: { interaction_type: true },
        });
        analytics.forEach((row) => {
          if (interactionCount[row.interaction_type] !== undefined) {
            interactionCount[row.interaction_type] += row._count.interaction_type;
          }
        });

        // Count employerprofiles by status
        const statuses = await prisma.employerprofiles.groupBy({
          by: ['status'],
          where: { video_resume_id: video.id },
          _count: { status: true },
        });
        statuses.forEach((row) => {
          if (statusCount[row.status] !== undefined) {
            statusCount[row.status] += row._count.status;
          }
        });
      })
    );

    res.json(
      convertBigInt({
        candidateProfile: [
          {
            ...candidate,
            interactions: interactionCount,
            statusCounts: statusCount,
          },
        ],
        videoProfiles: videoProfiles,
      })
    );
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Core Filteration Helper Function
exports.helperFilterCandidates = async ({
  preferred_location,
  role,
  selectedSkills = [],
  experience,
  expected_salary,
  current_salary,
  score,
}) => {
  try {
    // Build jobseeker filter
    const jobseekerWhere = {};
    if (preferred_location) {
      // preferred_location is String? (nullable String), so Prisma does NOT support 'mode'
      // Only use 'contains' for substring match, case-sensitive
      jobseekerWhere.preferred_location = { contains: preferred_location };
    }

    // Fetch all matching candidates
    const candidates = await prisma.jobseeker.findMany({
      where: jobseekerWhere,
    });

    if (!candidates.length) {
      return [];
    }

    const candidateIds = candidates.map((cand) => cand.cand_id);

    // Build videoprofile filter
    const videoWhere = {
      cand_id: { in: candidateIds },
    };
    if (role) {
      // Remove unsupported 'mode' for role filter
      videoWhere.role = { equals: role };
    }
    if (experience) {
      // Map experience string to enum value for videoprofile_experience_range
      const experienceEnumMap = {
        "0-1": "ZeroToOne",
        "2-3": "TwoToThree",
        "3-5": "ThreeToFive"
      };
      // Only assign if experience is a valid enum value
      const normalizedExperience = experienceEnumMap[experience];
      if (normalizedExperience) {
        videoWhere.experience_range = normalizedExperience;
      }
      // If not a valid enum, do not assign experience_range (prevents Prisma error)
    }
    // Prisma can't filter on JSON fields directly, so we filter salary/score in JS after fetch
    // For skills, we use contains on the string
    // Removed skills filtering; only filter by role as requested

    // Fetch video profiles
    let videoProfiles = await prisma.videoprofile.findMany({
      where: videoWhere,
    });

    // Post-filter for JSON fields (score, salary)
    if (score) {
      videoProfiles = videoProfiles.filter((vp) => {
        try {
          const parsedScore = vp.score ? JSON.parse(vp.score) : null;
          // Support both { score: { Overall_Score: ... } } and flat { Overall_Score: ... }
          const overall = parsedScore?.score?.Overall_Score ?? parsedScore?.Overall_Score;
          return overall > score;
        } catch {
          return false;
        }
      });
    }
    if (expected_salary) {
      videoProfiles = videoProfiles.filter((vp) => {
        try {
          const parsedSalary = vp.salary ? JSON.parse(vp.salary) : null;
          return parsedSalary?.expected == expected_salary;
        } catch {
          return false;
        }
      });
    }
    if (current_salary) {
      videoProfiles = videoProfiles.filter((vp) => {
        try {
          const parsedSalary = vp.salary ? JSON.parse(vp.salary) : null;
          return parsedSalary?.current == current_salary;
        } catch {
          return false;
        }
      });
    }

    // Merge candidate details into video profiles
    const mergedResults = videoProfiles.map((video) => {
      const candidateDetail = candidates.find(
        (cand) => cand.cand_id === video.cand_id
      );
      return {
        ...video,
        candidateDetails: candidateDetail
          ? [{
              profile_picture: candidateDetail.profile_picture,
              cand_name: candidateDetail.cand_name,
              preferred_location: candidateDetail.preferred_location,
            }]
          : [null],
      };
    });

    return mergedResults;
  } catch (error) {
    console.error("Error filtering candidates with Prisma:", error);
    throw "Failed to retrieve candidate profiles.";
  }
};

// Filter candidates profiles
exports.filterCandidate = async (req, res) => {
  try {
    const {
      preferred_location,
      role,
      selectedSkills,
      experience,
      expected_salary,
      current_salary,
      score,
    } = req.query;

    const parsedSkills = selectedSkills ? JSON.parse(selectedSkills) : [];
    const candidates = await this.helperFilterCandidates({
      preferred_location,
      role,
      selectedSkills: parsedSkills,
      experience,
      expected_salary,
      current_salary,
      score,
    });

    // Convert BigInt values before sending to avoid JSON serialization error
    function convertBigIntDeep(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigIntDeep);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigIntDeep(v)])
        );
      }
      return obj;
    }
    return res.json(convertBigIntDeep({ candidateProfiles: candidates }));
  } catch (error) {
    console.error("Error filtering candidates:", error);
    // Convert BigInt values before sending to avoid JSON serialization error
    function convertBigIntDeep(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigIntDeep);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigIntDeep(v)])
        );
      }
      return obj;
    }
    res.status(500).send(convertBigIntDeep({ message: "Failed to filter candidate profiles." }));
  }
};

// Fetch All candidates profiles with pagination
exports.listAllCandidates = async (req, res) => {
  try {
    // Get pagination parameters from the query
    const page = parseInt(req.query.page) || 1; // Default to page 1
    const pageSize = parseInt(req.query.pageSize) || 10; // Default to 10 items per page
    const skip = (page - 1) * pageSize;

    // Fetch paginated video profiles
    const [videoProfiles, totalCandidates] = await Promise.all([
      prisma.videoprofile.findMany({
        skip,
        take: pageSize,
      }),
      prisma.videoprofile.count(),
    ]);

    // Fetch candidate details for each video profile
    const candidateDetailsPromises = videoProfiles.map(async (candidate) => {
      const detail = await prisma.jobseeker.findUnique({
        where: { cand_id: candidate.cand_id },
        select: {
          profile_picture: true,
          cand_name: true,
          preferred_location: true,
        },
      });
      return {
        ...candidate,
        candidateDetails: detail
          ? [
              {
                profile_picture: detail.profile_picture,
                cand_name: detail.cand_name,
                preferred_location: detail.preferred_location,
              },
            ]
          : [null],
      };
    });

    const detailedCandidates = await Promise.all(candidateDetailsPromises);

    const totalPages = Math.ceil(totalCandidates / pageSize);

    // Convert BigInt values before sending to avoid JSON serialization error
    function convertBigIntDeep(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigIntDeep);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigIntDeep(v)])
        );
      }
      return obj;
    }
    res.json(
      convertBigIntDeep({
        candidateProfiles: detailedCandidates,
        pagination: {
          currentPage: page,
          pageSize: pageSize,
          totalCandidates: totalCandidates,
          totalPages: totalPages,
        },
      })
    );
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Fetch All candidates profiles with pagination
exports.listSuggestedCandidates = async (req, res) => {
  try {
    const emp_id = req.query.emp_id;

    // Find all video_resume_ids already shortlisted by this employer
    const shortlisted = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
      select: { video_resume_id: true },
    });
    const shortlistedIds = shortlisted.map((ep) => ep.video_resume_id);

    // Find video profiles not shortlisted by this employer
    let candidates = await prisma.videoprofile.findMany({
      where: {
        NOT: shortlistedIds.length
          ? { id: { in: shortlistedIds } }
          : undefined,
      },
    });

    // Shuffle and take 10 random candidates
    candidates = candidates
      .sort(() => Math.random() - 0.5)
      .slice(0, 10);

    // Fetch candidate details for each video profile
    const candidateDetailsPromises = candidates.map(async (candidate) => {
      const detail = await prisma.jobseeker.findUnique({
        where: { cand_id: candidate.cand_id },
        select: {
          profile_picture: true,
          cand_name: true,
          preferred_location: true,
        },
      });
      return {
        ...candidate,
        candidateDetails: detail
          ? [
              {
                profile_picture: detail.profile_picture,
                cand_name: detail.cand_name,
                preferred_location: detail.preferred_location,
              },
            ]
          : [null],
      };
    });

    const detailedCandidates = await Promise.all(candidateDetailsPromises);

    // Convert BigInt values before sending to avoid JSON serialization error
    res.json(
      convertBigInt({
        candidateProfiles: detailedCandidates,
      })
    );
  } catch (error) {
    console.error("Error listing candidate and video profiles:", error);
    res.status(500).send("Failed to list candidate and video profiles.");
  }
};

// Delete a video profile by video profile ID
exports.deleteVideoProfileById = async (req, res) => {
  const { video_profile_id } = req.params;

  try {
    // Check if the video profile exists
    const profile = await prisma.videoprofile.findFirst({
      where: { video_profile_id },
      select: { id: true }
    });

    if (!profile) {
      return res.status(404).json({
        message: "No video profile found for this video profile ID.",
      });
    }
    const deleteId = profile.id;

    // Delete the video profile
    await prisma.videoprofile.delete({
      where: { id: deleteId },
    });

    res.status(200).json({
      message: "Video profile deleted successfully for video profile ID.",
      data: {
        video_profile_id,
      },
    });
  } catch (error) {
    console.error("Error deleting video profile by video profile ID:", error);
    res.status(500).json({ message: "Failed to delete the video profile." });
  }
};

// Fetch a video profile by video profile ID
exports.fetchVideoProfileById = async (req, res) => {
  // Accept both :vpid and :video_profile_id for flexibility
  const video_profile_id = req.params.vpid || req.params.video_profile_id;
  try {
    // Try to fetch by video_profile_id if the param is not numeric, else by id
    let profile = null;
    // Try both lookups for maximum compatibility
    profile = null;
    // Only query by id if the value is a valid integer, otherwise by video_profile_id as string
    profile = null;
    const asNumber = Number(video_profile_id);
    if (!isNaN(asNumber) && Number.isInteger(asNumber) && asNumber > 0) {
      // Try by id (auto-increment PK)
      profile = await prisma.videoprofile.findUnique({
        where: { id: asNumber },
      });
    }
    // Always attempt to fetch by video_profile_id if not found by id
    if (
      !profile &&
      typeof video_profile_id === "string" &&
      video_profile_id.trim() !== ""
    ) {
      // If all digits, convert to BigInt for comparison
      let vpidValue = video_profile_id;
      if (/^\d+$/.test(video_profile_id)) {
        try {
          vpidValue = BigInt(video_profile_id);
        } catch {}
      }
      profile = await prisma.videoprofile.findFirst({
        where: { video_profile_id: vpidValue },
      });
    }

    if (!profile) {
      return res.status(404).json({
        message: "No video profile found for this video profile ID.",
      });
    }

    // Always include questionsAndAnswers array, mapping DB questions field
    let questionsArr = [];
    try {
      if (profile.questions) {
        if (typeof profile.questions === "string") {
          questionsArr = JSON.parse(profile.questions);
        } else if (Array.isArray(profile.questions)) {
          questionsArr = profile.questions;
        }
      }
    } catch (e) {
      questionsArr = [];
    }
    // Guarantee array of question objects with all fields (including timestamp)
    const questionsAndAnswers = Array.isArray(questionsArr)
      ? questionsArr.map(q => ({ ...q }))
      : [];

    // Convert BigInt values before sending
    res.status(200).json(
      convertBigInt({
        message: "Video profile fetched successfully.",
        data: profile,
        questionsAndAnswers,
      })
    );
  } catch (error) {
    console.error("Error fetching video profile by video profile ID:", error);
    res.status(500).json({
      message: "Failed to fetch the video profile.",
      error: error && error.message ? error.message : error
    });
  }
};

// Add Video Profile Analytics API
exports.addAnalytics = async (req, res) => {
  const { employer_id, profile_id, interaction_type } = req.body;

  // Validate required fields
  if (!interaction_type || !employer_id || !profile_id) {
    return res
      .status(400)
      .json({ message: "Interaction Type, Employer ID, and Profile ID are required." });
  }

  // Validate interaction_type value
  if (!["view", "click"].includes(interaction_type)) {
    return res
      .status(400)
      .json({ message: "Invalid interaction type. Allowed values are 'view' and 'click'." });
  }

  try {
    // Check if employer exists
    const employer = await prisma.employer.findUnique({
      where: { id: Number(employer_id) },
      select: { id: true },
    });
    if (!employer) {
      return res.status(400).json({ message: "Invalid employer ID." });
    }

    // Check if profile exists
    const profile = await prisma.videoprofile.findUnique({
      where: { id: Number(profile_id) },
      select: { id: true },
    });
    if (!profile) {
      return res.status(400).json({ message: "Invalid profile ID." });
    }

    if (interaction_type === "view") {
      // Check for duplicate view
      const duplicate = await prisma.analytics.findFirst({
        where: {
          employer_id: Number(employer_id),
          profile_id: Number(profile_id),
          interaction_type: "view",
        },
      });
      if (duplicate) {
        return res.status(400).json({
          message: "Duplicate interaction: the same interaction type already exists for this employer and profile."
        });
      }
    }

    // Insert analytics record
    const analytics = await prisma.analytics.create({
      data: {
        employer_id: Number(employer_id),
        profile_id: Number(profile_id),
        interaction_type,
        timestamp: new Date(),
      },
    });

    res.status(201).json({
      message: "Analytics data added successfully.",
      data: analytics.id,
    });
  } catch (error) {
    console.error("Error adding analytics:", error);
    res
      .status(500)
      .json({ message: "Failed to add analytics data.", error });
  }
};

// 🎯 MEMBERSHIP STATUS API: Get candidate's membership status and Visume limits
exports.getCandidateMembershipStatus = async (req, res) => {
  const { cand_id } = req.params;

  if (!cand_id) {
    return res.status(400).json({
      message: "Candidate ID is required"
    });
  }

  try {
    const membershipStatus = await checkCandidateMembershipStatus(cand_id);

    return res.status(200).json({
      message: "Membership status retrieved successfully",
      data: {
        candidateId: cand_id,
        canCreateVisume: membershipStatus.canCreateVisume,
        currentVisumeCount: membershipStatus.currentVisumeCount,
        allowedVisumes: membershipStatus.allowedVisumes,
        planName: membershipStatus.planName,
        hasActivePlan: membershipStatus.hasActivePlan,
        planEndDate: membershipStatus.planEndDate,
        needsUpgrade: !membershipStatus.canCreateVisume,
        whatsappContact: "[WHATSAPP_NUMBER_PLACEHOLDER]"
      }
    });
  } catch (error) {
    console.error("Error fetching candidate membership status:", error);
    return res.status(500).json({
      message: "Failed to fetch membership status",
      error: error.message
    });
  }
};

// 🎯 ADMIN API: Get all candidates with membership information for admin dashboard
exports.getAdminCandidates = async (req, res) => {
  try {
    // Get all jobseekers with their basic information
    const candidates = await prisma.jobseeker.findMany({
      select: {
        id: true,
        cand_id: true,
        cand_name: true,
        cand_email: true,
        cand_mobile: true,
        profile_picture: true,
        preferred_location: true,
        cand_skills: true,
        created_at: true,
        user: {
          select: {
            created_at: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    // Get membership information for each candidate
    const candidatesWithMembership = await Promise.all(
      candidates.map(async (candidate) => {
        try {
          // Get candidate's current plan
          const candidatePlan = await prisma.jobseekerplans.findFirst({
            where: { cand_id: candidate.id },
            include: { plans: true },
            orderBy: { start_date: 'desc' }
          });

          // Count current Visumes
          const currentVisumeCount = await prisma.videoprofile.count({
            where: { cand_id: candidate.cand_id }
          });

          // Calculate membership status
          let membershipStatus = 'active';
          let allowedVisumes = 1; // Default free plan
          let planName = 'Free Plan';
          let planEndDate = null;

          if (candidatePlan) {
            allowedVisumes = candidatePlan.credits || 1;
            planName = candidatePlan.plans.plan_name || 'Free Plan';
            planEndDate = candidatePlan.end_date;

            // Determine membership status
            if (currentVisumeCount >= allowedVisumes) {
              membershipStatus = 'warning';
            } else if (candidatePlan.end_date && new Date() > candidatePlan.end_date) {
              membershipStatus = 'expired';
            }
          } else {
            // No plan found, check if they've used their free Visume
            if (currentVisumeCount >= 1) {
              membershipStatus = 'warning';
            }
          }

          return {
            id: candidate.id,
            cand_id: candidate.cand_id,
            name: candidate.cand_name,
            email: candidate.cand_email,
            phone: candidate.cand_mobile,
            profile_picture: candidate.profile_picture,
            location: candidate.preferred_location,
            skills: candidate.cand_skills || '',
            joinDate: candidate.created_at,
            currentVisumeCount,
            allowedVisumes,
            planName,
            planEndDate,
            membershipStatus,
            canCreateVisume: currentVisumeCount < allowedVisumes,
            needsUpgrade: currentVisumeCount >= allowedVisumes
          };
        } catch (error) {
          console.error(`Error processing candidate ${candidate.cand_id}:`, error);
          // Return basic info if membership calculation fails
          return {
            id: candidate.id,
            cand_id: candidate.cand_id,
            name: candidate.cand_name,
            email: candidate.cand_email,
            phone: candidate.cand_mobile,
            profile_picture: candidate.profile_picture,
            location: candidate.preferred_location,
            skills: candidate.cand_skills || '',
            joinDate: candidate.created_at,
            currentVisumeCount: 0,
            allowedVisumes: 1,
            planName: 'Free Plan',
            planEndDate: null,
            membershipStatus: 'active',
            canCreateVisume: true,
            needsUpgrade: false
          };
        }
      })
    );

    return res.status(200).json({
      message: "Admin candidates retrieved successfully",
      data: {
        candidates: candidatesWithMembership,
        totalCount: candidatesWithMembership.length
      }
    });
  } catch (error) {
    console.error("Error fetching admin candidates:", error);
    return res.status(500).json({
      message: "Failed to fetch candidates for admin",
      error: error.message
    });
  }
};

// 🎯 ADMIN API: Update candidate's Visume limit following employer credit management pattern
exports.updateCandidateLimit = async (req, res) => {
  const { cand_id } = req.params;
  const { visume_limit } = req.body;

  if (!cand_id) {
    return res.status(400).json({
      message: "Candidate ID is required"
    });
  }

  if (visume_limit === undefined || visume_limit === null) {
    return res.status(400).json({
      message: "Visume limit is required"
    });
  }

  const newVisumeLimit = parseInt(visume_limit);
  if (isNaN(newVisumeLimit) || newVisumeLimit < 0) {
    return res.status(400).json({
      message: "Visume limit must be a non-negative number"
    });
  }

  try {
    // Get candidate's database ID from cand_id
    const candidate = await prisma.jobseeker.findUnique({
      where: { cand_id: cand_id },
      select: { id: true, cand_name: true }
    });

    if (!candidate) {
      return res.status(404).json({
        message: "Candidate not found"
      });
    }

    // Get or create a plan for the candidate
    let candidatePlan = await prisma.jobseekerplans.findFirst({
      where: { cand_id: candidate.id },
      include: { plans: true },
      orderBy: { start_date: 'desc' }
    });

    if (candidatePlan) {
      // Update existing plan with new visume limit and reset usage
      await prisma.jobseekerplans.update({
        where: { id: candidatePlan.id },
        data: {
          visume_limit: newVisumeLimit,
          visumes_used: 0, // Reset usage history when admin updates limit
          credits: newVisumeLimit, // Keep credits in sync for backward compatibility
          // Extend end date if increasing limits
          end_date: newVisumeLimit > (candidatePlan.visume_limit || candidatePlan.credits || 1)
            ? new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
            : candidatePlan.end_date
        }
      });
    } else {
      // Create new plan entry - find or create a default plan
      let defaultPlan = await prisma.plans.findFirst({
        where: { role: 'js' },
        orderBy: { id: 'asc' }
      });

      if (!defaultPlan) {
        // Create a default plan if none exists
        defaultPlan = await prisma.plans.create({
          data: {
            plan_name: 'Admin Assigned Plan',
            plan_description: 'Plan assigned by admin',
            plan_price: 0,
            plan_duration_days: 365,
            role: 'js',
            credits_assigned: newVisumeLimit,
            features: 'Admin managed plan'
          }
        });
      }

      await prisma.jobseekerplans.create({
        data: {
          cand_id: candidate.id,
          plan_id: defaultPlan.id,
          start_date: new Date(),
          end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          visume_limit: newVisumeLimit,
          visumes_used: 0,
          credits: newVisumeLimit // Keep credits in sync for backward compatibility
        }
      });
    }

    // Get updated membership status
    const updatedMembershipStatus = await checkCandidateMembershipStatus(cand_id);

    return res.status(200).json({
      message: `Visume limit updated and usage history reset for ${candidate.cand_name}`,
      data: {
        candidateId: cand_id,
        candidateName: candidate.cand_name,
        visumeLimit: newVisumeLimit,
        visumesUsed: 0, // Always 0 after reset
        currentVisumeCount: updatedMembershipStatus.currentVisumeCount,
        allowedVisumes: updatedMembershipStatus.allowedVisumes,
        planName: updatedMembershipStatus.planName,
        canCreateVisume: updatedMembershipStatus.canCreateVisume,
        usageReset: true
      }
    });
  } catch (error) {
    console.error("Error updating candidate limit:", error);
    return res.status(500).json({
      message: "Failed to update candidate limit",
      error: error.message
    });
  }
};
