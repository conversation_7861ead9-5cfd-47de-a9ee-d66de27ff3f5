import React, { useState } from "react";
import defaultProfileImg from "assets/img/default-profile.png";
import JobCard from "../components/JobCard";
import CreateVR from "../components/CreateVR/CreateVR";
import VideoProfileCard from "../components/VideoProfileCard";
import VidProfPopup from "../components/VidProfPopup";
import UpgradeModal from "../components/UpgradeModal";
import { HiOutlineSparkles, HiVideoCamera } from "react-icons/hi";
import SmLoader from "../../../components/SmLoader";
import {
  Eye,
  Video,
  ClipboardCheck,
  MousePointerClick,
  Unlock,
  ArrowUpCircle,
} from "lucide-react";

export default function DashboardContent({
  jstoken,
  candData,
  loadingInfo,
  togglePopupVR,
  profile_picture,
  imageError,
  setImageError,
  userStats,
  jobData,
  videoProfiles,
  isLoading,
  createVRpopup,
  showVideoProfilePopup,
  toggleVideoProfilePopup,
  membershipStatus,
  membershipLoading,
}) {
  // Add state for the upgrade modal
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Function to toggle the upgrade modal
  const toggleUpgradeModal = () => {
    setShowUpgradeModal(prev => !prev);
  };

  return (
    <>
      {jstoken ? (
        <div className="space-y-8 p-4">
          {/* Streamlined Header with Profile and Stats */}
          <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
            <div className="flex items-center justify-between p-6">
              {/* Left: Profile Section */}
              <div className="flex items-center gap-4">
                {imageError ? (
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-sm font-semibold text-white shadow-lg">
                    {candData && candData?.cand_name[0]?.toUpperCase()}
                  </div>
                ) : (
                  <img
                    className="h-12 w-12 rounded-full border-2 border-gray-200 object-cover shadow-md dark:border-gray-700"
                    src={profile_picture || defaultProfileImg}
                    alt={candData?.cand_name}
                    onError={(e) => {
                      if (e.target.src !== defaultProfileImg) {
                        e.target.onerror = null;
                        e.target.src = defaultProfileImg;
                      }
                      setImageError(true);
                    }}
                  />
                )}
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {loadingInfo ? (
                      <div className="h-5 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700" />
                    ) : (
                      candData.cand_name
                    )}
                  </h2>
                  <div className="flex items-center gap-3">
                    {/* Conditional rendering for plan badge or upgrade button */}
                    {membershipStatus && !membershipLoading && !membershipStatus.canCreateVisume ? (
                      <button 
                        onClick={toggleUpgradeModal}
                        className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-green-600 to-green-700 px-2.5 py-1 text-xs font-medium text-white hover:from-green-700 hover:to-green-800 transition-all duration-200"
                      >
                        <ArrowUpCircle className="w-3 h-3" />
                        Upgrade Plan
                      </button>
                    ) : (
                      <span className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 px-2.5 py-1 text-xs font-medium text-white">
                        {membershipStatus?.planName || "Free Plan"}
                      </span>
                    )}
                    <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                      <Video className="w-3 h-3" />
                      <span>
                        {membershipStatus ?
                          `${membershipStatus.visumesUsed || membershipStatus.currentVisumeCount || 0}/${membershipStatus.allowedVisumes} Visume${membershipStatus.allowedVisumes !== 1 ? 's' : ''}` :
                          '0/1 Visume'
                        }
                      </span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1 ml-2">
                        <div
                          className={`h-1 rounded-full transition-all duration-500 ${
                            membershipStatus && !membershipStatus.canCreateVisume
                              ? 'bg-gradient-to-r from-red-500 to-red-600'
                              : 'bg-gradient-to-r from-blue-500 to-purple-600'
                          }`}
                          style={{
                            width: membershipStatus ?
                              `${Math.min(((membershipStatus.visumesUsed || membershipStatus.currentVisumeCount || 0) / membershipStatus.allowedVisumes) * 100, 100)}%` :
                              "0%"
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Center: Stats */}
              <div className="hidden items-center gap-6 lg:flex">
                {userStats.map(({ count, label, Icon }) => (
                  <div
                    key={label}
                    className="flex items-center gap-2 rounded-lg bg-gray-50 px-4 py-3 dark:bg-gray-800"
                  >
                    <div className="rounded-lg bg-white p-1.5 dark:bg-gray-700">
                      <Icon className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <div className="text-xl font-bold leading-none text-gray-900 dark:text-white">
                        {count}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {label}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Right: Action Button */}
              <button
                onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-5 py-2.5 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Video className="h-4 w-4" />
                Create Visume
              </button>
            </div>

            {/* Mobile Stats */}
            <div className="border-t border-gray-200 p-4 dark:border-gray-800 lg:hidden">
              <div className="grid grid-cols-2 gap-4">
                {userStats.map(({ count, label, Icon }) => (
                  <div
                    key={label}
                    className="flex items-center gap-2 rounded-lg bg-gray-50 p-3 dark:bg-gray-800"
                  >
                    <div className="rounded bg-white p-1 dark:bg-gray-700">
                      <Icon className="h-3 w-3 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <div className="text-lg font-bold leading-none text-gray-900 dark:text-white">
                        {count}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {label}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Enhanced Suggested Jobs */}
            <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
              <div className="border-b border-gray-200 p-6 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 p-2 dark:from-purple-900/50 dark:to-pink-900/50">
                    <HiOutlineSparkles className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Suggested Jobs
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      AI-curated opportunities for you
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                {jobData.length > 0 ? (
                  <div className="max-h-96 space-y-3 overflow-y-auto scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 dark:scrollbar-track-gray-800 dark:scrollbar-thumb-gray-600">
                    {jobData.map((job) => (
                      <JobCard key={job.id} iconUrl={job.image} job={job} />
                    ))}
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-purple-100 to-pink-100 shadow-lg dark:from-purple-900/50 dark:to-pink-900/50">
                      <HiOutlineSparkles className="h-10 w-10 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">
                      AI is finding perfect matches
                    </h3>
                    <p className="mx-auto max-w-sm text-gray-500 dark:text-gray-400">
                      Our intelligent system is analyzing your profile and
                      skills to curate the best job opportunities just for you
                    </p>
                    <div className="mt-6">
                      <div className="flex justify-center">
                        <div className="flex space-x-1">
                          <div className="h-2 w-2 animate-bounce rounded-full bg-purple-600"></div>
                          <div
                            className="h-2 w-2 animate-bounce rounded-full bg-purple-600"
                            style={{ animationDelay: "0.1s" }}
                          ></div>
                          <div
                            className="h-2 w-2 animate-bounce rounded-full bg-purple-600"
                            style={{ animationDelay: "0.2s" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced Your Visumes - Right Column */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-gradient-to-br from-blue-100 to-cyan-100 p-2 dark:from-blue-900/50 dark:to-cyan-900/50">
                      <HiVideoCamera className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Your Visumes
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {membershipLoading ? (
                          "Loading membership status..."
                        ) : membershipStatus ? (
                          membershipStatus.statusText
                        ) : (
                          `${videoProfiles.length} video resumes created`
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Membership Status Badge and Upgrade Button */}
                  <div className="flex items-center gap-2">
                    {membershipStatus && !membershipLoading && (
                      <>
                        {membershipStatus.canCreateVisume ? (
                          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                            membershipStatus.statusColor === 'success'
                              ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                              : membershipStatus.statusColor === 'warning'
                              ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                              : membershipStatus.statusColor === 'error'
                              ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                              : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                          }`}>
                            {membershipStatus.planName}
                          </div>
                        ) : (
                          <button
                            onClick={toggleUpgradeModal}
                            className="px-3 py-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white text-xs font-medium rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                          >
                            Upgrade
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6">
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <SmLoader text={"Loading your video resumes..."} />
                  </div>
                ) : videoProfiles.length === 0 ? (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-100 to-cyan-100 shadow-lg dark:from-blue-900/50 dark:to-cyan-900/50">
                      <HiVideoCamera className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                      Ready to stand out?
                    </h3>
                    <p className="mx-auto mb-4 max-w-sm text-sm text-gray-500 dark:text-gray-400">
                      Create your first video resume and let your personality
                      shine through to potential employers
                    </p>
                    <button
                      onClick={membershipStatus && !membershipStatus.canCreateVisume ? toggleUpgradeModal : togglePopupVR}
                      className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-5 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      <Video className="h-4 w-4" />
                      Create Your First Visume
                    </button>
                  </div>
                ) : (
                  <div className="max-h-96 space-y-3 overflow-y-auto scrollbar-thin scrollbar-track-gray-100 scrollbar-thumb-gray-300 dark:scrollbar-track-gray-800 dark:scrollbar-thumb-gray-600">
                    {videoProfiles.map((profile) => (
                      <VideoProfileCard
                        key={profile.vpid}
                        profile={profile}
                        toggleVideoProfilePopup={toggleVideoProfilePopup}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <h2>Please SignIn</h2>
      )}

      {/* Start Popup for Video Resume */}
      {createVRpopup && (
        <CreateVR key="create-vr" togglePopupVR={togglePopupVR} />
      )}
      {/* End Popup for Video Resume */}

      {/* Video profile popup */}
      {showVideoProfilePopup && (
        <VidProfPopup
          key="vid-prof"
          toggleVideoProfilePopup={toggleVideoProfilePopup}
        />
      )}

      {/* Upgrade Modal */}
      <UpgradeModal 
        isOpen={showUpgradeModal} 
        onClose={toggleUpgradeModal} 
        membershipStatus={membershipStatus || {}}
      />
    </>
  );
}
